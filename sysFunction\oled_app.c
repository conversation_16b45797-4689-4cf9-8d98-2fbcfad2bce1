#include "mcu_cmic_gd32f470vet6.h"

extern uint16_t adc_value[1];
extern sampling_state_t sampling_state;
extern config_params_t system_config;

static uint32_t last_oled_update_time = 0; 


int oled_printf(uint8_t x, uint8_t y, const char *format, ...)
{
  char buffer[512]; 
  va_list arg;      
  int len;         

  va_start(arg, format);
  
  len = vsnprintf(buffer, sizeof(buffer), format, arg);
  va_end(arg);

  OLED_ShowStr(x, y, buffer, 16);
  return len;
}

void oled_task(void)
{

    static uint32_t test_counter = 0;

    test_counter++;

    if(!sampling_state.is_sampling) {
        
        OLED_ShowStr(0, 0, "system idle", 16);  
        OLED_ShowStr(0, 2, "            ", 16); 
        last_oled_update_time = 0; 
    } else {

        uint32_t current_time = get_system_ms();


        if(last_oled_update_time == 0 || current_time >= (sampling_state.sample_period * 1000) + last_oled_update_time) {
            last_oled_update_time = current_time;

     
            uint32_t timestamp = get_unix_timestamp();
            local_time_t local_time = timestamp_to_local_time(timestamp);

            float voltage_r = (adc_value[0] * 3.3f / 4095.0f);
            float voltage = voltage_r * system_config.ratio_ch0;

            int volt_int = (int)voltage;
            int volt_frac = (int)((voltage - volt_int) * 100);

     
            char time_str[16];
            char volt_str[16];
            sprintf(time_str, "%02d:%02d:%02d", local_time.hour, local_time.minute, local_time.second);
            sprintf(volt_str, "%d.%02d V    ", volt_int, volt_frac);  

            OLED_ShowStr(0, 0, time_str, 16);
            OLED_ShowStr(0, 2, volt_str, 16);
        }
    }
}


