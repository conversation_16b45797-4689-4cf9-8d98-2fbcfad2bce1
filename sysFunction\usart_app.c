#include "mcu_cmic_gd32f470vet6.h"

/* =================== ȫ�ֱ������� =================== */

__IO uint16_t tx_count = 0;
__IO uint8_t rx_flag = 0;
uint8_t uart_dma_buffer[512] = {0};
extern __IO uint32_t prescaler_a, prescaler_s;
extern uint16_t adc_value[1];

// ȫ�����ò���
config_params_t system_config = {1.0f, 1.0f, 5}; 

// ����״̬
sampling_state_t sampling_state = {0, 5, 0, 0}; 

storage_state_t storage_state = {0, 0, 0, 0, 0};

volatile uint8_t waiting_for_ratio_input = 0;
volatile uint8_t waiting_for_limit_input = 0;
volatile uint8_t waiting_for_rtc_input = 0;

/* =================== USARTͨ��ģ�� =================== */

int my_printf(uint32_t usart_periph, const char *format, ...)
{
    char buffer[512];
    va_list arg;
    int len;

    va_start(arg, format);
    len = vsnprintf(buffer, sizeof(buffer), format, arg);
    va_end(arg);
    
    for(tx_count = 0; tx_count < len; tx_count++){
        usart_data_transmit(usart_periph, buffer[tx_count]);
        while(RESET == usart_flag_get(usart_periph, USART_FLAG_TBE));
    }
    
    return len;
}

/* =================== UART���������� =================== */
void uart_task(void)
{
    if(!rx_flag) return;
    

    char* command = (char*)uart_dma_buffer;
    

    int len = strlen(command);
    while(len > 0 && (command[len-1] == '\r' || command[len-1] == '\n' || command[len-1] == ' ')) {
        command[len-1] = '\0';
        len--;
    }
    
    if(len > 0) {
        process_command(command);
    }
    
    memset(uart_dma_buffer, 0, 256);
    rx_flag = 0;
}

/* =================== �����ģ�� =================== */
void process_command(char* command)
{
    if(waiting_for_ratio_input) {
        float input_value = atof(command);
        if(input_value >= 0.0f && input_value <= 100.0f) {
            system_config.ratio_ch0 = input_value;
            
            my_printf(USART0, "ratio modified success\r\n");
            my_printf(USART0, "Ratio = %.1f\r\n", system_config.ratio_ch0);
            
            char log_msg[64];
            sprintf(log_msg, "ratio config success to %.1f", input_value);
            log_operation(log_msg);
            

            save_config_to_file();
        } else {
            my_printf(USART0, "ratio invalid\r\n");
            my_printf(USART0, "Ratio = %.1f\r\n", system_config.ratio_ch0);
        }
        waiting_for_ratio_input = 0;
        return;
    }
    if(waiting_for_limit_input) {
        float input_value = atof(command);
        if(input_value >= 0.0f && input_value <= 200.0f) {
            system_config.limit_ch0 = input_value;

            my_printf(USART0, "limit modified success\r\n");
            my_printf(USART0, "limit = %.2f\r\n", system_config.limit_ch0);
            
            char log_msg[64];
            sprintf(log_msg, "limit config success to %.2f", input_value);
            log_operation(log_msg);
            
            save_config_to_file();
        } else {
            my_printf(USART0, "limit invalid\r\n");
            my_printf(USART0, "limit = %.2f\r\n", system_config.limit_ch0);
        }
        waiting_for_limit_input = 0;
        return;
    }

    if(waiting_for_rtc_input) {
        log_operation("rtc config");
        rtc_config_handler(command);
        waiting_for_rtc_input = 0;
        return;
    }

    if(strcmp(command, "test") == 0) {
        system_self_test();
    }
    else if(strcmp(command, "RTC Config") == 0) {
        my_printf(USART0, "Input Datetime\r\n");
        waiting_for_rtc_input = 1;
    }
    else if(strcmp(command, "RTC now") == 0) {
        rtc_show_current_time();
    }
    else if(strcmp(command, "conf") == 0) {
        config_read_handler();
    }
    else if(strcmp(command, "ratio") == 0) {
        ratio_setting_handler();
    }
    else if(strcmp(command, "limit") == 0) {
        limit_setting_handler();
    }
    else if(strcmp(command, "config save") == 0) {
        config_save_handler();
    }
    else if(strcmp(command, "config read") == 0) {
        config_read_flash_handler();
    }
    else if(strcmp(command, "start") == 0) {
        sampling_start_handler();
    }
    else if(strcmp(command, "stop") == 0) {
        sampling_stop_handler();
    }
    else if(strcmp(command, "hide") == 0) {
        hide_data_handler();
    }
    else if(strcmp(command, "unhide") == 0) {
        unhide_data_handler();
    }

}

















