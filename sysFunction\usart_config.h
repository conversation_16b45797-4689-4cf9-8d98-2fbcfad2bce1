#ifndef __USART_CONFIG_H__
#define __USART_CONFIG_H__

#include "stdint.h"
#include "gd32f4xx.h"

#ifdef __cplusplus
extern "C" {
#endif

// Configuration management function declarations
void ratio_setting_handler(void);
void limit_setting_handler(void);
void config_save_handler(void);
void config_read_flash_handler(void);
ErrStatus write_config_to_flash(config_params_t* config);
ErrStatus read_config_from_flash(config_params_t* config);

// Generic helper functions
void print_operation_result(const char* operation, ErrStatus result);
ErrStatus validate_config(config_params_t* config);
ErrStatus flash_write_verify(uint32_t addr, uint8_t* data, uint16_t size);

#ifdef __cplusplus
}
#endif

#endif /* __USART_CONFIG_H__ */
