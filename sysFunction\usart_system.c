#include "mcu_cmic_gd32f470vet6.h"

extern __IO uint32_t prescaler_a, prescaler_s;
extern storage_state_t storage_state;

void system_self_test(void)
{
    log_operation("system hardware test");

    my_printf(USART0, "======system selftest======\r\n");

    uint32_t flash_id = spi_flash_read_id();
    if(flash_id != 0x000000 && flash_id != 0xFFFFFF) {
        my_printf(USART0, "flash......ok\r\n");
    } else {
        my_printf(USART0, "flash......error\r\n");
    }

    DSTATUS sd_status = disk_initialize(0);
    if(sd_status == 0) {
        my_printf(USART0, "TF card......ok\r\n");
        my_printf(USART0, "flash ID:0x%06X\r\n", flash_id);
        uint32_t capacity = sd_card_capacity_get();
        my_printf(USART0, "TF card memory: %d KB\r\n", capacity);
        log_operation("test ok");
    } else {
        my_printf(USART0, "TF card.......error\r\n");
        my_printf(USART0, "flash ID:0x%06X\r\n", flash_id);
        my_printf(USART0, "can not find TF card\r\n");
        log_operation("test error: tf card not found");
    }
    uint32_t timestamp = get_unix_timestamp();
    local_time_t local_time = timestamp_to_local_time(timestamp);
    my_printf(USART0, "RTC: %04d-%02d-%02d %02d:%02d:%02d\r\n",local_time.year, local_time.month, local_time.day,local_time.hour, local_time.minute, local_time.second);
    my_printf(USART0, "======system selftest======\r\n");
}

void rtc_config_handler(char* time_str)
{
    int year, month, day, hour, minute, second;
    int parsed = 0;

    parsed = sscanf(time_str, "%d-%d-%d %d:%d:%d", &year, &month, &day, &hour, &minute, &second);
    if(parsed == 6) {
        if(year >= 2000 && year <= 2099 &&month >= 1 && month <= 12 &&
           day >= 1 && day <= 31 &&
           hour >= 0 && hour <= 23 &&
           minute >= 0 && minute <= 59 &&
           second >= 0 && second <= 59) {

            int UtcHour = hour - 8;
            int UtcDay = day;
            int UtcMonth = month;
            int UtcYear = year;

            if(UtcHour < 0) {
                UtcHour += 24;
                UtcDay--;

                if(UtcDay < 1) {
                    UtcMonth--;
                    if(UtcMonth < 1) {
                        UtcMonth = 12;
                        UtcYear--;
                    }
                    UtcDay = get_days_in_month(UtcMonth, UtcYear);
                }
            }

            extern rtc_parameter_struct rtc_initpara;

            rtc_initpara.year = dec_to_bcd(UtcYear - 2000);
            rtc_initpara.month = dec_to_bcd(UtcMonth);
            rtc_initpara.date = dec_to_bcd(UtcDay);
            rtc_initpara.day_of_week = 1;
            rtc_initpara.hour = dec_to_bcd(UtcHour);
            rtc_initpara.minute = dec_to_bcd(minute);
            rtc_initpara.second = dec_to_bcd(second);
            rtc_initpara.factor_asyn = prescaler_a;
            rtc_initpara.factor_syn = prescaler_s;
            rtc_initpara.am_pm = RTC_AM;
              if(rtc_init(&rtc_initpara) == SUCCESS) {
                my_printf(USART0, "RTC Config success\r\n");
                my_printf(USART0, "Time: %04d-%02d-%02d %02d:%02d:%02d\r\n",
                         year, month, day, hour, minute, second);

                char log_msg[64];
                sprintf(log_msg, "rtc config success to %04d-%02d-%02d %02d:%02d:%02d",
                       year, month, day, hour, minute, second);
                log_operation(log_msg);
            } 
        } 
    } 
}

void rtc_show_current_time(void)
{
    uint32_t timestamp = get_unix_timestamp();
    local_time_t local_time = timestamp_to_local_time(timestamp);

    my_printf(USART0, "Current Time: %04d-%02d-%02d %02d:%02d:%02d\r\n",
              local_time.year, local_time.month, local_time.day,
              local_time.hour, local_time.minute, local_time.second);
}

void read_device_id_from_flash(char* device_id, uint16_t max_len)
{
    // Read device ID from Flash
    spi_flash_buffer_read((uint8_t*)device_id, DEVICE_ID_FLASH_ADDR, max_len);

    // Check if the read ID is valid
    if(strncmp(device_id, DEFAULT_DEVICE_ID, 20) != 0) {
        // If invalid, use team device ID
        strncpy(device_id, DEFAULT_DEVICE_ID, max_len - 1);
        device_id[max_len - 1] = '\0';

        // Write team device ID to Flash using unified function
        spi_flash_sector_erase(DEVICE_ID_FLASH_ADDR);
        spi_flash_wait_for_write_end();
        spi_flash_buffer_write((uint8_t*)device_id, DEVICE_ID_FLASH_ADDR, strlen(device_id) + 1);
    }
}

void system_startup_init(void)
{
    char device_id[32];
    
    oled_task();

    my_printf(USART0, "====system init====\r\n");
    read_device_id_from_flash(device_id, sizeof(device_id));
    my_printf(USART0, "Device_ID:%s\r\n", device_id);
    storage_init();
    log_operation("system init");
    my_printf(USART0, "====system ready====\r\n");
}

void power_count_reset_handler(void)
{
    storage_state.log_id = 0;
    save_log_id_to_flash(0);
    log_operation("power count reset");
}
