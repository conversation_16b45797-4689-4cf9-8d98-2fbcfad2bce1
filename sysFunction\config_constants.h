#ifndef __CONFIG_CONSTANTS_H__
#define __CONFIG_CONSTANTS_H__

#ifdef __cplusplus
extern "C" {
#endif

// Flash address configuration
#define CONFIG_FLASH_ADDR 0x1000
#define DEVICE_ID_FLASH_ADDR 0x3000

// Parameter range configuration
#define RATIO_MIN 0.0f
#define RATIO_MAX 100.0f
#define LIMIT_MIN 0.0f
#define LIMIT_MAX 200.0f

// Default value configuration
#define DEFAULT_RATIO 1.0f
#define DEFAULT_LIMIT 1.0f
#define DEFAULT_SAMPLE_PERIOD 5

// Message templates
#define MSG_SUCCESS "success"
#define MSG_FAILED "failed"
#define MSG_SAVE_SUCCESS "save parameters to flash"
#define MSG_SAVE_FAILED "Failed to save parameters to flash"
#define MSG_READ_SUCCESS "read parameters from flash"
#define MSG_READ_FAILED "Failed to read parameters from flash"

// Input prompts
#define PROMPT_RATIO_RANGE "Input value(0~100):"
#define PROMPT_LIMIT_RANGE "Input value(0~200):"

#ifdef __cplusplus
}
#endif

#endif /* __CONFIG_CONSTANTS_H__ */
