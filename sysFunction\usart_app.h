#ifndef __USART_APP_H__
#define __USART_APP_H__

#include "stdint.h"
#include "gd32f4xx.h"
#include "sd_app.h"  // 包含存储相关函数声明
#include "rtc_app.h"

#ifdef __cplusplus
extern "C" {
#endif

// 配置管理函数声明
typedef struct {
    float ratio_ch0;
    float limit_ch0;
    uint32_t sample_period; // 采样周期，单位秒
} config_params_t;


typedef struct {
    uint8_t is_sampling;       // Sampling status: 0=stopped, 1=running
    uint32_t sample_period;    // Sampling period in seconds
    uint32_t last_sample_time; // Last sampling time (system tick)
    uint8_t hide_mode;         // Data hiding mode: 0=normal display, 1=hidden display
} sampling_state_t;


int my_printf(uint32_t usart_periph, const char *format, ...);


void uart_task(void);

void process_command(char* command);

// 数据存储相关结构体
typedef struct {
    uint8_t sample_count;           // 当前sample文件中的数据条数
    uint8_t overlimit_count;        // 当前overlimit文件中的数据条数
    uint8_t hidedata_count;         // 当前hidedata文件中的数据条数
    uint32_t log_id;                // 日志文件ID（上电次数）
    uint8_t hide_storage_enabled;   // 是否启用加密存储
} storage_state_t;

// 系统初始化函数
void system_config_init(void);

#ifdef __cplusplus
}
#endif

#endif
