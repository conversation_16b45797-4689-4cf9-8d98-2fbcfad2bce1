#ifndef __USART_SYSTEM_H__
#define __USART_SYSTEM_H__

#include "stdint.h"

#ifdef __cplusplus
extern "C" {
#endif

// 设备ID存储地址（Flash）
#define DEVICE_ID_FLASH_ADDR    0x3000
// 队伍设备ID
#define DEFAULT_DEVICE_ID       "2025-CIMC-2025968733"

// 系统管理函数声明
void system_self_test(void);
void rtc_config_handler(char* time_str);
void rtc_show_current_time(void);
void read_device_id_from_flash(char* device_id, uint16_t max_len);
void system_startup_init(void);
void power_count_reset_handler(void);

#ifdef __cplusplus
}
#endif

#endif /* __USART_SYSTEM_H__ */
