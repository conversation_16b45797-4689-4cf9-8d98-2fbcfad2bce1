#include "mcu_cmic_gd32f470vet6.h"
#include "usart_app.h"
#include "ff.h"

// ԭ�е�SD�����Ա���
FIL fdst;
uint16_t i = 0, count, result = 0;
UINT br, bw;
sd_card_info_struct sd_cardinfo;
BYTE buffer[128];
BYTE filebuffer[128];

// �ļ�ϵͳ�ʹ洢���ȫ�ֱ���
FATFS fs;
extern config_params_t system_config;
extern storage_state_t storage_state;

// �ļ��������
FIL current_log_file;
FIL current_sample_file;
FIL current_overlimit_file;
FIL current_hidedata_file;

// �ļ��򿪱�־
uint8_t log_file_open = 0;
uint8_t sample_file_open = 0;
uint8_t overlimit_file_open = 0;
uint8_t hidedata_file_open = 0;

ErrStatus memory_compare(uint8_t* src, uint8_t* dst, uint16_t length) 
{
    while(length --){
        if(*src++ != *dst++)
            return ERROR;
    }
    return SUCCESS;
}

void sd_fatfs_init(void)
{
    nvic_irq_enable(SDIO_IRQn, 0, 0);					
}

void config_read_handler(void)
{
    FIL config_file;
    FRESULT result;
    char line_buffer[128];
    result = f_open(&config_file, "config.ini", FA_READ);
    if(result != FR_OK) {
        my_printf(USART0, "config.ini file not found.\r\n");
        return;
    }
    

    uint8_t ratio_section_found = 0;
    uint8_t limit_section_found = 0;
    uint8_t ratio_parsed = 0;
    uint8_t limit_parsed = 0;
    
    while(f_gets(line_buffer, sizeof(line_buffer), &config_file) != NULL) {
 
        char* newline = strchr(line_buffer, '\n');
        if(newline) *newline = '\0';
        char* carriage = strchr(line_buffer, '\r');
        if(carriage) *carriage = '\0';
        
  
        if(strlen(line_buffer) == 0 || line_buffer[0] == ';' || line_buffer[0] == '#') {
            continue;
        }
        
        if(line_buffer[0] == '[') {
            if(strstr(line_buffer, "[Ratio]") != NULL) {
                ratio_section_found = 1;
                limit_section_found = 0;
            } else if(strstr(line_buffer, "[Limit]") != NULL) {
                limit_section_found = 1;
                ratio_section_found = 0;
            } else {
                ratio_section_found = 0;
                limit_section_found = 0;
            }
        }
  
        else if(ratio_section_found && strstr(line_buffer, "Ch0") != NULL) {
            char* equal_sign = strchr(line_buffer, '=');
            if(equal_sign != NULL) {
                equal_sign++; 
                while(*equal_sign == ' ') equal_sign++; 
                system_config.ratio_ch0 = atof(equal_sign);
                ratio_parsed = 1;
            }
        }
        else if(limit_section_found && strstr(line_buffer, "Ch0") != NULL) {
            char* equal_sign = strchr(line_buffer, '=');
            if(equal_sign != NULL) {
                equal_sign++; 
                while(*equal_sign == ' ') equal_sign++;
                system_config.limit_ch0 = atof(equal_sign);
                limit_parsed = 1;
            }
        }
    }
    
    // �ر��ļ�
    f_close(&config_file);
    

    if(ratio_parsed && limit_parsed) {

        my_printf(USART0, "Ratio: %.1f\r\n", system_config.ratio_ch0);
        my_printf(USART0, "Limit: %.2f\r\n", system_config.limit_ch0);

        if(write_config_to_flash(&system_config) == SUCCESS) {
            my_printf(USART0, "config read success\r\n");
        }
    }
}

// =================== ���ݴ洢��غ��� ===================


void get_datetime_string(char* datetime_str)
{
    uint32_t timestamp = get_unix_timestamp();
    local_time_t local_time = timestamp_to_local_time(timestamp);
    
    sprintf(datetime_str, "%04d%02d%02d%02d%02d%02d",
            local_time.year, local_time.month, local_time.day,
            local_time.hour, local_time.minute, local_time.second);
}


void create_storage_directories(void)
{
    FRESULT result;

    f_mkdir("sample");
    f_mkdir("overLimit");
    f_mkdir("log");
    f_mkdir("hideData");

}


uint32_t get_next_log_id(void)
{
    uint32_t log_id_flash_addr = 0x2000;
    uint32_t stored_log_id;
    
    spi_flash_buffer_read((uint8_t*)&stored_log_id, log_id_flash_addr, sizeof(uint32_t));
    
    if(stored_log_id == 0xFFFFFFFF || stored_log_id > 10000) {
        stored_log_id = 0; 
    }
    
    return stored_log_id;
}


void save_log_id_to_flash(uint32_t log_id)
{
    uint32_t log_id_flash_addr = 0x2000;
    
    
    spi_flash_sector_erase(log_id_flash_addr);
    spi_flash_wait_for_write_end();
    

    spi_flash_buffer_write((uint8_t*)&log_id, log_id_flash_addr, sizeof(uint32_t));
}


void storage_init(void)
{
    FRESULT result;
    
 
    DSTATUS sd_status = disk_initialize(0);
    if(sd_status != RES_OK) {
        return;
    }
    

    result = f_mount(0, &fs);
    if(result != FR_OK) {
        return;
    }

 
    create_storage_directories();

 
    storage_state.log_id = get_next_log_id();

  
    char log_filename[64];
    sprintf(log_filename, "log/log%u.txt", storage_state.log_id);

    result = f_open(&current_log_file, log_filename, FA_CREATE_ALWAYS | FA_WRITE);
    if(result == FR_OK) {
        log_file_open = 1;
    } else {
    }
    
  
    save_log_id_to_flash(storage_state.log_id + 1);
}


void log_operation(const char* operation)
{
    if(!log_file_open) return;
    
    uint32_t timestamp = get_unix_timestamp();
    local_time_t local_time = timestamp_to_local_time(timestamp);
    
    char log_entry[256];
  
    sprintf(log_entry, "%04d-%02d-%02d %02d:%02d:%02d %s\r\n",
            local_time.year, local_time.month, local_time.day,
            local_time.hour, local_time.minute, local_time.second,
            operation);
    
    UINT bytes_written;
    FRESULT result = f_write(&current_log_file, log_entry, strlen(log_entry), &bytes_written);
    if(result == FR_OK && bytes_written == strlen(log_entry)) {
        f_sync(&current_log_file);
    } else {

    }
}


void store_sample_data(float voltage, uint8_t over_limit)
{
    
    if(storage_state.hide_storage_enabled) {
        store_hidedata(voltage, over_limit);
        return;
    }
    
    FRESULT result;
    
    
    if(!sample_file_open || storage_state.sample_count >= 10) {
       
        if(sample_file_open) {
            f_close(&current_sample_file);
            sample_file_open = 0;
        }
        
        
        char datetime_str[16];
        get_datetime_string(datetime_str);
        char filename[64];
        sprintf(filename, "sample/sampleData%s.txt", datetime_str);
        
        result = f_open(&current_sample_file, filename, FA_CREATE_ALWAYS | FA_WRITE);
        if(result == FR_OK) {
            sample_file_open = 1;
            storage_state.sample_count = 0;
            
           
        } 
    }
    
    // д������
    if(sample_file_open) {
        uint32_t timestamp = get_unix_timestamp();
        local_time_t local_time = timestamp_to_local_time(timestamp);
        
        char data_line[128];
        // ��ʽ��2025-01-01 00:30:10 1.5V
        sprintf(data_line, "%04d-%02d-%02d %02d:%02d:%02d %.1fV\r\n",
                local_time.year, local_time.month, local_time.day,
                local_time.hour, local_time.minute, local_time.second,
                voltage);
        
        UINT bytes_written;
        result = f_write(&current_sample_file, data_line, strlen(data_line), &bytes_written);
        if(result == FR_OK && bytes_written == strlen(data_line)) {
            f_sync(&current_sample_file);
            storage_state.sample_count++;
        } 
    }
}

// �洢����ֵ����
void store_overlimit_data(float voltage)
{
    FRESULT result;
    
    // �����Ҫ�½��ļ����ļ�δ��
    if(!overlimit_file_open || storage_state.overlimit_count >= 10) {
        // �رյ�ǰ�ļ�
        if(overlimit_file_open) {
            f_close(&current_overlimit_file);
            overlimit_file_open = 0;
        }
        
        // �������ļ� - ʹ��overLimit�ļ��к���ȷ���ļ�����ʽ
        char datetime_str[16];
        get_datetime_string(datetime_str);
        char filename[64];
        sprintf(filename, "overLimit/overLimit%s.txt", datetime_str);
        
        result = f_open(&current_overlimit_file, filename, FA_CREATE_ALWAYS | FA_WRITE);
        if(result == FR_OK) {
            overlimit_file_open = 1;
            storage_state.overlimit_count = 0;
           
        } 
    }
    
    // д������
    if(overlimit_file_open) {
        uint32_t timestamp = get_unix_timestamp();
        local_time_t local_time = timestamp_to_local_time(timestamp);
        
        char data_line[128];
        // ��ʽ��2025-01-01 00:30:10 30V limit 10V
        sprintf(data_line, "%04d-%02d-%02d %02d:%02d:%02d %.0fV limit %.0fV\r\n",
                local_time.year, local_time.month, local_time.day,
                local_time.hour, local_time.minute, local_time.second,
                voltage, system_config.limit_ch0);
        
        UINT bytes_written;
        result = f_write(&current_overlimit_file, data_line, strlen(data_line), &bytes_written);
        if(result == FR_OK && bytes_written == strlen(data_line)) {
            f_sync(&current_overlimit_file);
            storage_state.overlimit_count++;
        } 
    }
}

// �洢��������
void store_hidedata(float voltage, uint8_t over_limit)
{
    FRESULT result;
    
    // �����Ҫ�½��ļ����ļ�δ��
    if(!hidedata_file_open || storage_state.hidedata_count >= 10) {
        // �رյ�ǰ�ļ�
        if(hidedata_file_open) {
            f_close(&current_hidedata_file);
            hidedata_file_open = 0;
        }
        
        // �������ļ� - ʹ��hideData�ļ��к���ȷ���ļ�����ʽ
        char datetime_str[16];
        get_datetime_string(datetime_str);
        char filename[64];
        sprintf(filename, "hideData/hideData%s.txt", datetime_str);
        
        result = f_open(&current_hidedata_file, filename, FA_CREATE_ALWAYS | FA_WRITE);
        if(result == FR_OK) {
            hidedata_file_open = 1;
            storage_state.hidedata_count = 0;

        } 
    }
    
    // д������
    if(hidedata_file_open) {
        uint32_t timestamp = get_unix_timestamp();
        local_time_t local_time = timestamp_to_local_time(timestamp);
        
        char data_line[256];
 
        uint16_t voltage_int = (uint16_t)voltage;
        uint16_t voltage_frac = (uint16_t)((voltage - voltage_int) * 65536);
        uint32_t voltage_hex = (voltage_int << 16) | voltage_frac;

        sprintf(data_line, "%04d-%02d-%02d %02d:%02d:%02d %.3fV\r\nhide: %08X%08X\r\n",
                local_time.year, local_time.month, local_time.day,
                local_time.hour, local_time.minute, local_time.second,
                voltage, timestamp, voltage_hex);
        
        UINT bytes_written;
        result = f_write(&current_hidedata_file, data_line, strlen(data_line), &bytes_written);
        if(result == FR_OK && bytes_written == strlen(data_line)) {
            f_sync(&current_hidedata_file);
            storage_state.hidedata_count++;
        } 
    }
}

// �������õ�TF��config.ini�ļ��ĺ���
void save_config_to_file(void)
{
    FIL config_file;
    FRESULT result;
    char write_buffer[256];
    UINT bytes_written;
    
    // �������config.ini�ļ�����д��
    result = f_open(&config_file, "config.ini", FA_CREATE_ALWAYS | FA_WRITE);
    if(result != FR_OK) {
        return;
    }
    
    // ���������ļ�����
    sprintf(write_buffer, "[Ratio]\r\nCh0=");
    result = f_write(&config_file, write_buffer, strlen(write_buffer), &bytes_written);
    
    sprintf(write_buffer, "%.1f\r\n\r\n[Limit]\r\nCh0=", system_config.ratio_ch0);
    result = f_write(&config_file, write_buffer, strlen(write_buffer), &bytes_written);
    
    sprintf(write_buffer, "%.2f\r\n", system_config.limit_ch0);
    result = f_write(&config_file, write_buffer, strlen(write_buffer), &bytes_written);
    
    // �ر��ļ�
    f_close(&config_file);
}



