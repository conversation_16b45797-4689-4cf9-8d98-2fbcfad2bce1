#include "mcu_cmic_gd32f470vet6.h"

extern config_params_t system_config;
extern storage_state_t storage_state;
extern sampling_state_t sampling_state;

void system_config_init(void)
{
    config_params_t temp_config;

    if(read_config_from_flash(&temp_config) == SUCCESS) {
        system_config = temp_config;
        sampling_state.sample_period = system_config.sample_period;
    } else {
        // Use default values from constants
        system_config.ratio_ch0 = DEFAULT_RATIO;
        system_config.limit_ch0 = DEFAULT_LIMIT;
        system_config.sample_period = DEFAULT_SAMPLE_PERIOD;

        write_config_to_flash(&system_config);
    }
}

int main(void)
{
#ifdef __FIRMWARE_VERSION_DEFINE
    uint32_t fw_ver = 0;
#endif
    systick_config();
    init_cycle_counter(false);
    delay_ms(200); 
    
#ifdef __FIRMWARE_VERSION_DEFINE
    fw_ver = gd32f4xx_firmware_version_get();
  
#endif 
    
    bsp_led_init();
    bsp_btn_init();
    bsp_oled_init();
    bsp_gd25qxx_init();
    bsp_usart_init();
    bsp_adc_init();
    bsp_rtc_init();
    sd_fatfs_init();
    app_btn_init();
    OLED_Init();
    system_config_init();
    system_startup_init();
    scheduler_init();
    
    while(1) {
        scheduler_run();
    }
}

#ifdef GD_ECLIPSE_GCC
/* retarget the C library printf function to the USART, in Eclipse GCC environment */
int __io_putchar(int ch)
{
    usart_data_transmit(EVAL_COM0, (uint8_t)ch);
    while(RESET == usart_flag_get(EVAL_COM0, USART_FLAG_TBE));
    return ch;
}
#else

int fputc(int ch, FILE *f)
{
    usart_data_transmit(USART0, (uint8_t)ch);
    while(RESET == usart_flag_get(USART0, USART_FLAG_TBE));
    return ch;
}
#endif 
