#include "mcu_cmic_gd32f470vet6.h"

extern uint16_t adc_value[1];
extern uint16_t convertarr[CONVERT_NUM];  

extern sampling_state_t sampling_state;
extern config_params_t system_config;

uint32_t last_tick_sampling;
uint32_t last_tick_star;

void adc_task(void)
{
    convertarr[0] = adc_value[0];
    SamplingTask();
}

void  SamplingTask(void)
{
    if(!sampling_state.is_sampling) {
        return;
    }
    uint32_t TickCounter = get_system_ms();
    if(TickCounter >= 500 + last_tick_star)  
    {
        last_tick_star = TickCounter;
        LED1_TOGGLE;
    }
    if(TickCounter >= (sampling_state.sample_period * 1000) + last_tick_sampling) {
        last_tick_sampling = TickCounter;
        float voltage_r = (adc_value[0] * 3.3f / 4095.0f);
        float voltage = voltage_r * system_config.ratio_ch0;
        uint8_t OverTheBestLimit = (voltage > system_config.limit_ch0) ? 1 : 0;
        print_sample_data(voltage, OverTheBestLimit);
        if(OverTheBestLimit) {
            LED2_ON;
        } else {
            LED2_OFF;
        }
    }
}

