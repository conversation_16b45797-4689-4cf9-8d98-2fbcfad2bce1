#include "mcu_cmic_gd32f470vet6.h"

uint8_t ucLed[6] = {0,0,0,0,0,0}; 


void led_disp(uint8_t *ucLed)
{

    uint8_t temp = 0x00;

    static uint8_t temp_old = 0xff;

    for (int i = 0; i < 6; i++)
    {

        if (ucLed[i]) temp |= (1<<i); 
    }


    if (temp_old != temp)
    {

        LED1_SET(temp & 0x01);
        LED1_SET(temp & 0x02);
        

        temp_old = temp;
    }
}


void led_task(void)
{
    led_disp(ucLed);
}

