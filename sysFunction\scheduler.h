/*!
    \file    scheduler.h
    \brief   Task scheduler header file for time-slice based task management

    \version V0.10
    \date    2025-06-05
    \author  <PERSON><PERSON><PERSON><PERSON>
    \company MCUSTUDIO
*/

#ifndef SCHEDULER_H
#define SCHEDULER_H

#include "stdint.h"

#ifdef __cplusplus
extern "C" {
#endif

/*!
    \brief      Initialize the task scheduler
    \param[in]  none
    \param[out] none
    \retval     none
    \note       This function calculates the number of tasks in the scheduler array
                and prepares the scheduler for execution
*/
void scheduler_init(void);

/*!
    \brief      Run the task scheduler main loop
    \param[in]  none
    \param[out] none
    \retval     none
    \note       This function checks each task's timing and executes tasks
                that are ready to run based on their configured rate
*/
void scheduler_run(void);

#ifdef __cplusplus
}
#endif

#endif /* SCHEDULER_H */
