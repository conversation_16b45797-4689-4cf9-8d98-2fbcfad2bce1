#include "mcu_cmic_gd32f470vet6.h"

extern sampling_state_t sampling_state;
extern storage_state_t storage_state;
extern config_params_t system_config;

void sampling_start_handler(void)
{
    sampling_state.is_sampling = 1;
    sampling_state.sample_period = system_config.sample_period;
    sampling_state.last_sample_time = 0;
    
    my_printf(USART0, "Periodic Sampling\r\n");
    my_printf(USART0, "sample cycle: %ds\r\n", sampling_state.sample_period);
    OLED_Clear();
    oled_task();

    log_operation("sample start - cycle 5s (command)");
}

void sampling_stop_handler(void)
{
    sampling_state.is_sampling = 0;
    
    OLED_Clear();
    LED1_OFF;
    LED2_OFF;
    my_printf(USART0, "Periodic Sampling STOP\r\n");
    
    oled_task();
    
    log_operation("sample stop (command)");
}

void hide_data_handler(void)
{
    sampling_state.hide_mode = 1;
    storage_state.hide_storage_enabled = 1;  // 启用隐藏存储模式
}

void unhide_data_handler(void)
{
    sampling_state.hide_mode = 0;
    storage_state.hide_storage_enabled = 0; 
}

void print_sample_data(float voltage, uint8_t over_limit)
{
    store_sample_data(voltage, over_limit);
    
    if(over_limit) {
        store_overlimit_data(voltage);
    }
    
    if(sampling_state.hide_mode) {
        uint32_t timestamp = get_unix_timestamp();
        
        uint16_t voltage_int = (uint16_t)voltage;
        uint16_t voltage_frac = (uint16_t)((voltage - voltage_int) * 65536);
        
        uint32_t voltage_hex = (voltage_int << 16) | voltage_frac;
        
        if(over_limit) {
            my_printf(USART0, "%08X%08X*\r\n", timestamp, voltage_hex);
        } else {
            my_printf(USART0, "%08X%08X\r\n", timestamp, voltage_hex);
        }
    } else {
        uint32_t timestamp = get_unix_timestamp();
        local_time_t local_time = timestamp_to_local_time(timestamp);
        
        if(over_limit) {
            my_printf(USART0, "%04d-%02d-%02d %02d:%02d:%02d ch0=", 
                     local_time.year, local_time.month, local_time.day,
                     local_time.hour, local_time.minute, local_time.second);
            my_printf(USART0, "%.1fV OverLimit(%.1fV)!\r\n", voltage, system_config.limit_ch0);
        } else {
            my_printf(USART0, "%04d-%02d-%02d %02d:%02d:%02d ch0=", 
                     local_time.year, local_time.month, local_time.day,
                     local_time.hour, local_time.minute, local_time.second);
            my_printf(USART0, "%.1fV\r\n", voltage);
        }
    }
}
