# 函数文档标准和模板

## 1. 文档格式标准

### 1.1 基本格式
采用统一的 `/** */` 格式，使用 Doxygen 风格的标签。

### 1.2 标准模板

```c
/**
 * @brief 函数功能的简洁描述（一行内完成）
 * 
 * 详细的功能描述，包括函数的作用、处理逻辑、特殊行为等。
 * 如果需要多行描述，可以在这里展开说明。
 * 
 * @param param1 参数1的描述，包括类型、用途、取值范围等
 * @param param2 参数2的描述，包括类型、用途、取值范围等
 * @return 返回值描述，包括返回值类型、含义、可能的返回值等
 * @note 特殊说明、注意事项、使用限制等（可选）
 * @warning 警告信息，如安全注意事项（可选）
 * @example 使用示例（可选，仅对复杂函数使用）
 */
```

## 2. 编码规范

### 2.1 字符编码
- **强制使用 UTF-8 编码**
- 所有中文注释必须使用 UTF-8 编码保存
- 避免使用 GBK 或其他编码格式

### 2.2 注释语言
- 函数文档使用中文
- 行内注释使用中文，位于代码右侧
- 技术术语可以使用英文

## 3. 不同类型函数的文档要求

### 3.1 简单函数（无参数或简单逻辑）
```c
/**
 * @brief 启动周期性采样
 * 
 * 设置采样状态为运行，更新采样周期，并记录操作日志。
 * 
 * @return 无返回值
 * @note 调用此函数前确保系统已正确初始化
 */
void sampling_start_handler(void);
```

### 3.2 复杂函数（多参数、复杂逻辑）
```c
/**
 * @brief 格式化输出到指定USART端口
 * 
 * 类似printf的功能，支持格式化字符串输出到指定的USART端口。
 * 内部使用512字节缓冲区，支持可变参数列表。
 * 
 * @param usart_periph USART外设编号（如USART0、USART1等）
 * @param format 格式化字符串，支持标准printf格式
 * @param ... 可变参数列表，对应格式化字符串中的占位符
 * @return 实际发送的字符数量
 * @note 最大支持512字符的输出，超出部分将被截断
 * @warning 确保USART端口已正确初始化后再调用此函数
 */
int my_printf(uint32_t usart_periph, const char *format, ...);
```

### 3.3 配置类函数
```c
/**
 * @brief 将配置参数写入Flash存储器
 * 
 * 将系统配置参数保存到Flash的指定地址，并进行数据完整性验证。
 * 写入前会先擦除对应扇区，写入后会读回验证数据正确性。
 * 
 * @param config 指向配置参数结构体的指针，不能为NULL
 * @return SUCCESS 写入成功，ERROR 写入失败或验证失败
 * @note Flash地址固定为0x1000，每次写入会擦除整个扇区
 * @warning 频繁写入Flash会影响其寿命，建议仅在必要时调用
 */
ErrStatus write_config_to_flash(config_params_t* config);
```

### 3.4 处理类函数
```c
/**
 * @brief 处理接收到的串口命令
 * 
 * 解析并执行从串口接收到的命令字符串。支持多种命令类型，
 * 包括系统测试、参数配置、采样控制等。具有状态机逻辑，
 * 可处理需要用户输入的交互式命令。
 * 
 * @param command 以NULL结尾的命令字符串，已去除换行符
 * @return 无返回值
 * @note 支持的命令列表请参考帮助信息输出
 * @warning 命令字符串不能为NULL，且必须是有效的C字符串
 */
void process_command(char* command);
```

## 4. 特殊标签使用说明

### 4.1 @param 标签
- 格式：`@param 参数名 参数描述`
- 必须包含参数的类型、用途、取值范围
- 对于指针参数，说明是否可以为NULL
- 对于数组参数，说明数组大小要求

### 4.2 @return 标签
- 格式：`@return 返回值描述`
- void函数使用：`@return 无返回值`
- 枚举返回值要说明各个值的含义
- 指针返回值要说明NULL的情况

### 4.3 @note 标签
- 用于重要的使用说明
- 前置条件和后置条件
- 性能相关的注意事项
- 与其他函数的关联关系

### 4.4 @warning 标签
- 安全相关的警告
- 可能导致系统异常的情况
- 资源使用的限制

## 5. 示例对比

### 5.1 修改前（存在问题）
```c
// 系统自检
void system_self_test(void)
```

### 5.2 修改后（标准格式）
```c
/**
 * @brief 执行系统硬件自检
 * 
 * 检测Flash存储器、TF卡、RTC等硬件组件的工作状态，
 * 并输出检测结果到调试串口。检测过程会记录到系统日志。
 * 
 * @return 无返回值
 * @note 检测结果通过DEBUG_USART输出，确保串口已初始化
 */
void system_self_test(void);
```

## 6. 质量检查清单

- [ ] 使用UTF-8编码，中文显示正常
- [ ] @brief描述简洁明确（一行内）
- [ ] 所有参数都有@param说明
- [ ] 返回值有@return说明
- [ ] 复杂函数有详细的功能描述
- [ ] 特殊情况有@note或@warning说明
- [ ] 格式符合项目统一标准
