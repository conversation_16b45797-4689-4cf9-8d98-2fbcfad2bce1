File_name,flash percent,flash,ram,Code,RO_data,RW_data,ZI_data
c_w.l,18.139751%,12819,96,12268,551,0,96
sdio_sdcard.o,10.146035%,7170,68,7134,0,36,32
ff.o,9.686138%,6845,6,6826,13,6,0
oled.o,5.626309%,3976,22,1242,2712,22,0
sd_app.o,3.404653%,2406,3648,2386,0,20,3628
btod.o,3.045226%,2152,0,2152,0,0,0
ebtn.o,2.929190%,2070,60,2070,0,0,60
fz_wm.l,2.153733%,1522,0,1506,16,0,0
usart_system.o,1.913171%,1352,0,1352,0,0,0
mcu_cmic_gd32f470vet6.o,1.884870%,1332,592,1312,0,20,572
gd32f4xx_dma.o,1.884870%,1332,0,1332,0,0,0
scanf_fp.o,1.799966%,1272,0,1272,0,0,0
usart_app.o,1.713647%,1211,559,1176,0,35,524
usart_config.o,1.576385%,1114,0,956,158,0,0
_printf_fp_dec.o,1.491481%,1054,0,1054,0,0,0
perf_counter.o,1.400917%,990,80,906,4,80,0
gd25qxx.o,1.400917%,990,0,990,0,0,0
_scanf.o,1.250920%,884,0,884,0,0,0
gd32f4xx_rcu.o,1.222618%,864,0,864,0,0,0
m_wm.l,1.134884%,802,0,802,0,0,0
_printf_fp_hex.o,1.134884%,802,0,764,38,0,0
scanf_hexfp.o,1.132054%,800,0,800,0,0,0
usart_sampling.o,1.013188%,716,0,716,0,0,0
system_gd32f4xx.o,0.987717%,698,4,694,0,4,0
gd32f4xx_adc.o,0.925454%,654,0,654,0,0,0
gd32f4xx_usart.o,0.911304%,644,0,644,0,0,0
gd32f4xx_sdio.o,0.888662%,628,0,628,0,0,0
rtc_app.o,0.798098%,564,0,564,0,0,0
btn_app.o,0.735835%,520,112,394,14,112,0
gd32f4xx_i2c.o,0.701874%,496,0,496,0,0,0
startup_gd32f450_470.o,0.696213%,492,2048,64,428,0,2048
gd32f4xx_rtc.o,0.684893%,484,0,484,0,0,0
unicode.o,0.656591%,464,0,88,376,0,0
__printf_flags_ss_wp.o,0.578763%,409,0,392,17,0,0
bigflt0.o,0.532065%,376,0,228,148,0,0
oled_app.o,0.515085%,364,8,356,0,8,0
dmul.o,0.481123%,340,0,340,0,0,0
_scanf_int.o,0.469802%,332,0,332,0,0,0
lc_ctype_c.o,0.447161%,316,0,44,272,0,0
diskio.o,0.447161%,316,0,316,0,0,0
scanf_infnan.o,0.435841%,308,0,308,0,0,0
narrow.o,0.376408%,266,0,266,0,0,0
gd32f4xx_gpio.o,0.370748%,262,0,262,0,0,0
adc_app.o,0.350937%,248,8,240,0,8,0
lludivv7m.o,0.336786%,238,0,238,0,0,0
ldexp.o,0.322635%,228,0,228,0,0,0
gd32f4xx_misc.o,0.305655%,216,0,216,0,0,0
_printf_wctomb.o,0.277353%,196,0,188,8,0,0
_printf_hex_int_ll_ptr.o,0.266033%,188,0,148,40,0,0
_printf_intcommon.o,0.251882%,178,0,178,0,0,0
systick.o,0.237731%,168,4,164,0,4,0
gd32f4xx_it.o,0.237731%,168,0,168,0,0,0
strtod.o,0.232071%,164,0,164,0,0,0
scheduler.o,0.232071%,164,64,100,0,64,0
dnaninf.o,0.220751%,156,0,156,0,0,0
perfc_port_default.o,0.217920%,154,0,154,0,0,0
main.o,0.217920%,154,0,154,0,0,0
strncmp.o,0.212260%,150,0,150,0,0,0
frexp.o,0.198109%,140,0,140,0,0,0
fnaninf.o,0.198109%,140,0,140,0,0,0
rt_memcpy_v6.o,0.195279%,138,0,138,0,0,0
lludiv10.o,0.195279%,138,0,138,0,0,0
strcmpv7m.o,0.181129%,128,0,128,0,0,0
_printf_fp_infnan.o,0.181129%,128,0,128,0,0,0
_printf_longlong_dec.o,0.175468%,124,0,124,0,0,0
dleqf.o,0.169808%,120,0,120,0,0,0
deqf.o,0.169808%,120,0,120,0,0,0
_printf_dec.o,0.169808%,120,0,120,0,0,0
_printf_oct_int_ll.o,0.158488%,112,0,112,0,0,0
drleqf.o,0.152827%,108,0,108,0,0,0
gd32f4xx_spi.o,0.147167%,104,0,104,0,0,0
retnan.o,0.141507%,100,0,100,0,0,0
rt_memcpy_w.o,0.141507%,100,0,100,0,0,0
d2f.o,0.138677%,98,0,98,0,0,0
scalbn.o,0.130186%,92,0,92,0,0,0
__dczerorl2.o,0.127356%,90,0,90,0,0,0
memcmp.o,0.124526%,88,0,88,0,0,0
f2d.o,0.121696%,86,0,86,0,0,0
strncpy.o,0.121696%,86,0,86,0,0,0
_printf_str.o,0.116036%,82,0,82,0,0,0
rt_memclr_w.o,0.110375%,78,0,78,0,0,0
_printf_pad.o,0.110375%,78,0,78,0,0,0
sys_stackheap_outer.o,0.104715%,74,0,74,0,0,0
llsdiv.o,0.101885%,72,0,72,0,0,0
lc_numeric_c.o,0.101885%,72,0,44,28,0,0
rt_memclr.o,0.096225%,68,0,68,0,0,0
dunder.o,0.090564%,64,0,64,0,0,0
_wcrtomb.o,0.090564%,64,0,64,0,0,0
_sgetc.o,0.090564%,64,0,64,0,0,0
strlen.o,0.087734%,62,0,62,0,0,0
__0sscanf.o,0.084904%,60,0,60,0,0,0
atof.o,0.079244%,56,0,56,0,0,0
vsnprintf.o,0.073584%,52,0,52,0,0,0
__scatter.o,0.073584%,52,0,52,0,0,0
fpclassify.o,0.067923%,48,0,48,0,0,0
trapv.o,0.067923%,48,0,48,0,0,0
_printf_char_common.o,0.067923%,48,0,48,0,0,0
scanf_char.o,0.062263%,44,0,44,0,0,0
_printf_wchar.o,0.062263%,44,0,44,0,0,0
_printf_char.o,0.062263%,44,0,44,0,0,0
__2sprintf.o,0.062263%,44,0,44,0,0,0
_printf_charcount.o,0.056603%,40,0,40,0,0,0
llshl.o,0.053773%,38,0,38,0,0,0
libinit2.o,0.053773%,38,0,38,0,0,0
strstr.o,0.050942%,36,0,36,0,0,0
init_aeabi.o,0.050942%,36,0,36,0,0,0
_printf_truncate.o,0.050942%,36,0,36,0,0,0
systick_wrapper_ual.o,0.045282%,32,0,32,0,0,0
_chval.o,0.039622%,28,0,28,0,0,0
__scatter_zi.o,0.039622%,28,0,28,0,0,0
dcmpi.o,0.033962%,24,0,24,0,0,0
_rserrno.o,0.031131%,22,0,22,0,0,0
strchr.o,0.028301%,20,0,20,0,0,0
gd32f4xx_pmu.o,0.028301%,20,0,20,0,0,0
isspace.o,0.025471%,18,0,18,0,0,0
exit.o,0.025471%,18,0,18,0,0,0
fpconst.o,0.022641%,16,0,0,16,0,0
dcheck1.o,0.022641%,16,0,16,0,0,0
rt_ctype_table.o,0.022641%,16,0,16,0,0,0
_snputc.o,0.022641%,16,0,16,0,0,0
__printf_wp.o,0.019811%,14,0,14,0,0,0
dretinf.o,0.016981%,12,0,12,0,0,0
sys_exit.o,0.016981%,12,0,12,0,0,0
__rtentry2.o,0.016981%,12,0,12,0,0,0
fretinf.o,0.014151%,10,0,10,0,0,0
fpinit.o,0.014151%,10,0,10,0,0,0
rtexit2.o,0.014151%,10,0,10,0,0,0
_sputc.o,0.014151%,10,0,10,0,0,0
_printf_ll.o,0.014151%,10,0,10,0,0,0
_printf_l.o,0.014151%,10,0,10,0,0,0
scanf2.o,0.011321%,8,0,8,0,0,0
rt_locale_intlibspace.o,0.011321%,8,0,8,0,0,0
rt_errno_addr_intlibspace.o,0.011321%,8,0,8,0,0,0
libspace.o,0.011321%,8,96,8,0,0,96
__main.o,0.011321%,8,0,8,0,0,0
istatus.o,0.008490%,6,0,6,0,0,0
heapauxi.o,0.008490%,6,0,6,0,0,0
_printf_x.o,0.008490%,6,0,6,0,0,0
_printf_u.o,0.008490%,6,0,6,0,0,0
_printf_s.o,0.008490%,6,0,6,0,0,0
_printf_p.o,0.008490%,6,0,6,0,0,0
_printf_o.o,0.008490%,6,0,6,0,0,0
_printf_n.o,0.008490%,6,0,6,0,0,0
_printf_ls.o,0.008490%,6,0,6,0,0,0
_printf_llx.o,0.008490%,6,0,6,0,0,0
_printf_llu.o,0.008490%,6,0,6,0,0,0
_printf_llo.o,0.008490%,6,0,6,0,0,0
_printf_lli.o,0.008490%,6,0,6,0,0,0
_printf_lld.o,0.008490%,6,0,6,0,0,0
_printf_lc.o,0.008490%,6,0,6,0,0,0
_printf_i.o,0.008490%,6,0,6,0,0,0
_printf_g.o,0.008490%,6,0,6,0,0,0
_printf_f.o,0.008490%,6,0,6,0,0,0
_printf_e.o,0.008490%,6,0,6,0,0,0
_printf_d.o,0.008490%,6,0,6,0,0,0
_printf_c.o,0.008490%,6,0,6,0,0,0
_printf_a.o,0.008490%,6,0,6,0,0,0
__rtentry4.o,0.008490%,6,0,6,0,0,0
scanf1.o,0.005660%,4,0,4,0,0,0
printf2.o,0.005660%,4,0,4,0,0,0
printf1.o,0.005660%,4,0,4,0,0,0
_printf_percent_end.o,0.005660%,4,0,4,0,0,0
use_no_semi.o,0.002830%,2,0,2,0,0,0
rtexit.o,0.002830%,2,0,2,0,0,0
libshutdown2.o,0.002830%,2,0,2,0,0,0
libshutdown.o,0.002830%,2,0,2,0,0,0
libinit.o,0.002830%,2,0,2,0,0,0
