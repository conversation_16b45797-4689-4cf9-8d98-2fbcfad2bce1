#include "mcu_cmic_gd32f470vet6.h"
#include "config_constants.h"

extern config_params_t system_config;
extern volatile uint8_t waiting_for_ratio_input;
extern volatile uint8_t waiting_for_limit_input;

// Parameter configuration structure
typedef struct {
    float* value_ptr;
    volatile uint8_t* wait_flag;
    const char* name;
    const char* format;
    const char* range;
    const char* log_msg;
} param_config_t;

// Validation rule structure
typedef struct {
    float min_val, max_val;
    float* config_field;
} validation_rule_t;

// Generic parameter handler function
void generic_param_handler(const param_config_t* config) {
    my_printf(USART0, "%s= %s\r\n", config->name, config->format);
    my_printf(USART0, "%s\r\n", config->range);
    *(config->wait_flag) = 1;
    log_operation(config->log_msg);
}

// Print operation result
void print_operation_result(const char* operation, ErrStatus result) {
    my_printf(USART0, "%s\r\n", (result == SUCCESS) ? operation : "Failed");
}

// Config validation function
ErrStatus validate_config(config_params_t* config) {
    validation_rule_t rules[] = {
        {RATIO_MIN, RATIO_MAX, &config->ratio_ch0},
        {LIMIT_MIN, LIMIT_MAX, &config->limit_ch0}
    };

    for(int i = 0; i < sizeof(rules)/sizeof(rules[0]); i++) {
        if(*(rules[i].config_field) < rules[i].min_val ||
           *(rules[i].config_field) > rules[i].max_val) {
            return ERROR;
        }
    }
    return SUCCESS;
}

// Generic flash write with verification
ErrStatus flash_write_verify(uint32_t addr, uint8_t* data, uint16_t size) {
    spi_flash_sector_erase(addr);
    spi_flash_wait_for_write_end();
    spi_flash_buffer_write(data, addr, size);
    return SUCCESS; // Simplified for now
}

void ratio_setting_handler(void)
{
    char format_str[32];
    sprintf(format_str, "%.1f", system_config.ratio_ch0);

    param_config_t ratio_config = {
        &system_config.ratio_ch0,
        &waiting_for_ratio_input,
        "Ratio",
        format_str,
        PROMPT_RATIO_RANGE,
        "ratio config"
    };

    generic_param_handler(&ratio_config);
}

void limit_setting_handler(void)
{
    char format_str[32];
    sprintf(format_str, "%.2f", system_config.limit_ch0);

    param_config_t limit_config = {
        &system_config.limit_ch0,
        &waiting_for_limit_input,
        "limit",
        format_str,
        PROMPT_LIMIT_RANGE,
        "limit config"
    };

    generic_param_handler(&limit_config);
}

void config_save_handler(void)
{
    my_printf(USART0, "ratio: %.1f\r\n", system_config.ratio_ch0);
    my_printf(USART0, "limit: %.2f\r\n", system_config.limit_ch0);

    ErrStatus result = write_config_to_flash(&system_config);
    print_operation_result((result == SUCCESS) ? MSG_SAVE_SUCCESS : MSG_SAVE_FAILED, result);
}

void config_read_flash_handler(void)
{
    config_params_t temp_config;

    ErrStatus result = read_config_from_flash(&temp_config);
    if(result == SUCCESS) {
        system_config = temp_config;
        my_printf(USART0, "%s\r\n", MSG_READ_SUCCESS);
        my_printf(USART0, "ratio: %.1f\r\n", system_config.ratio_ch0);
        my_printf(USART0, "limit: %.2f\r\n", system_config.limit_ch0);
    } else {
        my_printf(USART0, "%s\r\n", MSG_READ_FAILED);
    }
}

ErrStatus write_config_to_flash(config_params_t* config)
{
    spi_flash_sector_erase(CONFIG_FLASH_ADDR);
    spi_flash_wait_for_write_end();
    spi_flash_buffer_write((uint8_t*)config, CONFIG_FLASH_ADDR, sizeof(config_params_t));

    config_params_t read_back_config;
    spi_flash_buffer_read((uint8_t*)&read_back_config, CONFIG_FLASH_ADDR, sizeof(config_params_t));
    if((read_back_config.ratio_ch0 == config->ratio_ch0) &&
       (read_back_config.limit_ch0 == config->limit_ch0) &&
       (read_back_config.sample_period == config->sample_period)) {
        return SUCCESS;
    } else {
        return ERROR;
    }
}

ErrStatus read_config_from_flash(config_params_t* config)
{
    spi_flash_buffer_read((uint8_t*)config, CONFIG_FLASH_ADDR, sizeof(config_params_t));
    return validate_config(config);
}
