#ifndef MCU_CMIC_GD32F470VET6_H
#define MCU_CMIC_GD32F470VET6_H

#include "gd32f4xx.h"
#include "gd32f4xx_sdio.h"
#include "gd32f4xx_dma.h"
#include "systick.h"

#include "ebtn.h"
#include "oled.h"
#include "gd25qxx.h"
#include "sdio_sdcard.h"
#include "ff.h"
#include "diskio.h"

#include "sd_app.h"
#include "led_app.h"
#include "adc_app.h"
#include "oled_app.h"
#include "usart_app.h"
#include "usart_config.h"
#include "usart_sampling.h"
#include "usart_system.h"
#include "config_constants.h"
#include "rtc_app.h"
#include "btn_app.h"
#include "scheduler.h"

#include "perf_counter.h"

#include <stdint.h>
#include <stddef.h>
#include <stdlib.h>
#include <stdarg.h>
#include <string.h>
#include <stdio.h>

#ifdef __cplusplus
extern "C" {
#endif
/***************************************************************************************************************/
/* LED */

#define LED_PORT_E        GPIOE
#define LED_CLK_PORT_E    RCU_GPIOE


#define LED2_PIN        GPIO_PIN_3
#define LED1_PIN        GPIO_PIN_5

#define LED2_SET(x)     do { if(x) GPIO_BOP(LED_PORT_E) = LED2_PIN; else GPIO_BC(LED_PORT_E) = LED2_PIN; } while(0)
#define LED1_SET(x)     do { if(x) GPIO_BOP(LED_PORT_E) = LED1_PIN; else GPIO_BC(LED_PORT_E) = LED1_PIN; } while(0)


#define LED2_TOGGLE     do { GPIO_TG(LED_PORT_E) = LED2_PIN; } while(0)
#define LED1_TOGGLE     do { GPIO_TG(LED_PORT_E) = LED1_PIN; } while(0)

#define LED2_OFF         do { GPIO_BC(LED_PORT_E) = LED2_PIN; } while(0)
#define LED1_OFF         do { GPIO_BC(LED_PORT_E) = LED1_PIN; } while(0)


#define LED2_ON        do { GPIO_BOP(LED_PORT_E) = LED2_PIN; } while(0)
#define LED1_ON        do { GPIO_BOP(LED_PORT_E) = LED1_PIN; } while(0)


// FUNCTION
void bsp_led_init(void);

/***************************************************************************************************************/
/* KEY */
#define KEYE_PORT        GPIOE
#define KEYB_PORT        GPIOB
#define KEYA_PORT        GPIOA
#define KEYE_CLK_PORT    RCU_GPIOE
#define KEYB_CLK_PORT    RCU_GPIOB
#define KEYA_CLK_PORT    RCU_GPIOA

#define KEY4_PIN        GPIO_PIN_11
#define KEY3_PIN        GPIO_PIN_9
#define KEY2_PIN        GPIO_PIN_7
#define KEY1_PIN        GPIO_PIN_0
#define KEYW_PIN        GPIO_PIN_0

#define KEY1_READ       gpio_input_bit_get(KEYB_PORT, KEY1_PIN)
#define KEY2_READ       gpio_input_bit_get(KEYE_PORT, KEY2_PIN)
#define KEY3_READ       gpio_input_bit_get(KEYE_PORT, KEY3_PIN)
#define KEY4_READ       gpio_input_bit_get(KEYE_PORT, KEY4_PIN)
#define KEYW_READ       gpio_input_bit_get(KEYA_PORT, KEYW_PIN)

// FUNCTION
void bsp_btn_init(void);

/***************************************************************************************************************/

/* OLED */
#define I2C0_OWN_ADDRESS7      0x72
#define I2C0_SLAVE_ADDRESS7    0x82
#define I2C0_DATA_ADDRESS      (uint32_t)&I2C_DATA(I2C0)

#define OLED_PORT        GPIOB
#define OLED_CLK_PORT    RCU_GPIOB
#define OLED_DAT_PIN     GPIO_PIN_9
#define OLED_CLK_PIN     GPIO_PIN_8

// FUNCTION
void bsp_oled_init(void);

/***************************************************************************************************************/

/* gd25qxx */

#define SPI_PORT              GPIOB
#define SPI_CLK_PORT          RCU_GPIOB

#define SPI_NSS               GPIO_PIN_12
#define SPI_SCK               GPIO_PIN_13
#define SPI_MISO              GPIO_PIN_14
#define SPI_MOSI              GPIO_PIN_15

// FUNCTION
void bsp_gd25qxx_init(void);

/***************************************************************************************************************/

/* USART */
#define USART0_RDATA_ADDRESS      ((uint32_t)&USART_DATA(USART0))

#define USART_PORT                GPIOA
#define USARTI_CLK_PORT           RCU_GPIOA

#define USART_TX                  GPIO_PIN_9
#define USART_RX                  GPIO_PIN_10

// FUNCTION
void bsp_usart_init(void);

/***************************************************************************************************************/

/* ADC */
#define ADC1_PORT       GPIOC
#define ADC1_CLK_PORT   RCU_GPIOC

#define ADC1_PIN        GPIO_PIN_0

// FUNCTION
void bsp_adc_init(void);

/***************************************************************************************************************/
#define CONVERT_NUM                     (1)

/***************************************************************************************************************/

/* RTC */
//#define RTC_CLOCK_SOURCE_LXTAL  // 注释掉LXTAL，使用IRC32K
#define RTC_CLOCK_SOURCE_IRC32K
#define BKP_VALUE    0x32F0

// FUNCTION
int bsp_rtc_init(void);

/***************************************************************************************************************/

#ifdef __cplusplus
  }
#endif

#endif /* MCU_CMIC_GD32F470VET6_H */
